# 🔥 期货溢价套利系统专业开发规则

## 📋 项目定位与目标

### 🎯 **系统核心定位**
本系统是**通用期货溢价执行套利，现货溢价平仓的专业套利系统**，支持任何代币的期现套利交易。

### 💰 **核心套利逻辑**
```
期货溢价发现(+0.25%) → 开仓锁定差价 → 等待趋同 → 现货溢价发现(-0.3%) → 平仓获利(0.55%)
```

### 🎯 **最终目标**
- **机构级质量标准**: 达到金融机构级别的代码质量和系统稳定性
- **毫秒级执行性能**: <30ms机会发现，<100ms订单执行，<200ms跨交易所同步
- **零误差运行**: 100%数据一致性，>99.9%系统可用性，>98%对冲质量
- **全代币支持**: 支持30+代币×3交易所的通用套利能力
- **智能风险控制**: 多层次风险管理，自动异常恢复，实时监控告警

---

## 🏗️ 架构设计规则

### Rule 1: 统一模块架构原则
**强制要求**: 所有功能必须基于31个核心统一模块实现，禁止重复造轮子

#### 1.1 核心模块依赖规则
```python
# ✅ 正确：使用统一模块
from core.unified_order_spread_calculator import get_order_spread_calculator
from core.unified_depth_analyzer import get_depth_analyzer
from websocket.unified_connection_pool_manager import UnifiedConnectionPoolManager

# ❌ 错误：重复实现
class MySpreadCalculator:  # 禁止！已有统一计算器
    pass
```

#### 1.2 模块集成规则
- **必须**: 新功能优先扩展现有统一模块
- **必须**: 保持模块间接口一致性
- **禁止**: 绕过统一模块直接调用底层API
- **禁止**: 在业务代码中硬编码配置参数

### Rule 2: 一致性设计原则
**强制要求**: 严格遵循18大一致性维度，确保系统零误差运行

#### 2.1 时间戳一致性规则
```python
# ✅ 正确：使用统一时间戳处理
from websocket.unified_timestamp_processor import process_timestamp
timestamp = process_timestamp(raw_timestamp, exchange="gate")

# ❌ 错误：直接使用原始时间戳
timestamp = int(time.time() * 1000)  # 禁止！必须使用统一处理器
```

#### 2.2 差价计算一致性规则
```python
# ✅ 正确：使用统一差价计算器
calculator = get_order_spread_calculator()
result = calculator.calculate_order_based_spread(
    spot_orderbook, futures_orderbook, target_amount, "opening"
)

# ❌ 错误：自定义差价计算
spread = (futures_price - spot_price) / spot_price  # 禁止！必须使用统一计算器
```

### Rule 3: 数据流一致性规则
**强制要求**: 所有数据处理必须通过原子快照技术确保一致性

#### 3.1 原子快照使用规则
```python
# ✅ 正确：使用原子快照
snapshot = await self._get_atomic_snapshot(symbol)
spot_orderbook = snapshot['spot_orderbook']
futures_orderbook = snapshot['futures_orderbook']

# ❌ 错误：分别获取数据
spot_orderbook = await get_spot_orderbook(symbol)  # 禁止！可能存在时间差
futures_orderbook = await get_futures_orderbook(symbol)
```

---

## 💻 代码质量规则

### Rule 4: 代码结构规范
**强制要求**: 严格遵循模块化设计，保持代码清晰可维护

#### 4.1 类设计规则
```python
# ✅ 正确：清晰的类结构
class ArbitrageEngine:
    """套利引擎 - 负责套利流程控制"""

    def __init__(self, exchanges: Dict, config: Dict):
        self.exchanges = exchanges
        self.config = config
        self.logger = get_logger(self.__class__.__name__)

    async def start_arbitrage_scanning(self):
        """启动套利扫描"""
        pass
```

#### 4.2 方法命名规则
- **必须**: 使用动词开头的方法名 (`calculate_`, `execute_`, `monitor_`)
- **必须**: 异步方法使用`async def`声明
- **必须**: 私有方法使用`_`前缀
- **禁止**: 使用缩写或不明确的命名

#### 4.3 错误处理规则
```python
# ✅ 正确：统一错误处理
try:
    result = await self.execute_order(order_params)
except NetworkError as e:
    self.logger.error(f"网络错误: {e}")
    await self.handle_network_error(e)
except APIError as e:
    self.logger.error(f"API错误: {e}")
    await self.handle_api_error(e)
```

### Rule 5: 性能优化规则
**强制要求**: 所有关键路径必须满足性能指标要求

#### 5.1 执行性能规则
- **机会发现**: <30ms延迟
- **订单执行**: <100ms完成
- **数据同步**: <200ms跨交易所同步
- **错误恢复**: <5秒自动恢复

#### 5.2 缓存使用规则
```python
# ✅ 正确：使用统一缓存系统
from utils.cache_monitor import get_cache_manager
cache = get_cache_manager()
balance = await cache.get_balance(exchange, account_type)

# ❌ 错误：重复API调用
balance = await exchange.get_balance()  # 禁止！应使用缓存
```

---

## 🔧 技术实现规则

### Rule 6: WebSocket连接规则
**强制要求**: 使用统一WebSocket管理器，符合08文档v5.0标准

#### 6.1 连接管理规则
```python
# ✅ 正确：使用统一WebSocket管理器
from websocket.ws_manager import get_ws_manager
ws_manager = get_ws_manager()
await ws_manager.start_client("bybit", market_type="spot")

# ❌ 错误：直接创建WebSocket连接
import websockets
ws = await websockets.connect(url)  # 禁止！必须使用统一管理器
```

#### 6.2 数据处理规则
```python
# ✅ 正确：使用统一数据格式化器
from websocket.unified_data_formatter import format_orderbook_data
formatted_data = format_orderbook_data(raw_data, exchange="gate")

# ❌ 错误：手动数据处理
asks = [[float(p), float(q)] for p, q in raw_data['asks']]  # 禁止！
```

### Rule 7: 交易执行规则
**强制要求**: 使用统一执行引擎，确保原子性和一致性

#### 7.1 订单执行规则
```python
# ✅ 正确：使用统一执行引擎
execution_engine = ExecutionEngine(exchanges, config)
result = await execution_engine.execute_arbitrage_opportunity(opportunity)

# ❌ 错误：直接调用交易所API
await gate_exchange.place_order(...)  # 禁止！必须通过执行引擎
await bybit_exchange.place_order(...)
```

#### 7.2 对冲质量规则
```python
# ✅ 正确：验证对冲质量
hedge_quality = self.calculate_hedge_quality(spot_amount, futures_amount)
if hedge_quality < 0.98:  # 98%对冲质量要求
    raise HedgeQualityError(f"对冲质量不足: {hedge_quality:.2%}")
```

### Rule 8: 配置管理规则
**强制要求**: 所有配置从.env文件读取，禁止硬编码

#### 8.1 配置读取规则
```python
# ✅ 正确：从环境变量读取配置
MIN_SPREAD = float(os.getenv("MIN_SPREAD", "0.002"))
MAX_SPREAD = float(os.getenv("MAX_SPREAD", "0.05"))

# ❌ 错误：硬编码配置
MIN_SPREAD = 0.002  # 禁止！必须从.env读取
```

#### 8.2 配置验证规则
```python
# ✅ 正确：配置验证
def validate_config(config: Dict) -> bool:
    """验证配置有效性"""
    if config.get("MIN_SPREAD", 0) <= 0:
        raise ConfigError("MIN_SPREAD必须大于0")
    return True
```

---

## 🧪 测试与质量保证规则

### Rule 9: 测试覆盖规则
**强制要求**: 关键功能必须有完整的测试覆盖

#### 9.1 单元测试规则
```python
# ✅ 正确：完整的单元测试
class TestUnifiedSpreadCalculator:
    def test_calculate_spread_basic(self):
        """测试基础差价计算"""
        calculator = UnifiedOrderSpreadCalculator()
        result = calculator.calculate_order_based_spread(
            spot_orderbook, futures_orderbook, 100.0, "opening"
        )
        assert result.executable_spread > 0
        assert result.spot_execution_price > 0
```

#### 9.2 集成测试规则
- **必须**: 测试完整的套利流程
- **必须**: 测试跨交易所数据一致性
- **必须**: 测试异常情况处理
- **必须**: 测试性能指标达标

### Rule 10: 质量监控规则
**强制要求**: 实时监控系统质量指标

#### 10.1 性能监控规则
```python
# ✅ 正确：性能监控
@performance_monitor
async def execute_arbitrage(self, opportunity):
    """执行套利 - 自动性能监控"""
    start_time = time.time()
    try:
        result = await self._execute_trading(opportunity)
        execution_time = time.time() - start_time
        if execution_time > 0.1:  # 100ms阈值
            self.logger.warning(f"执行超时: {execution_time:.3f}s")
        return result
    except Exception as e:
        self.logger.error(f"执行失败: {e}")
        raise
```

---

## 🛡️ 安全与风险控制规则

### Rule 11: 风险控制规则
**强制要求**: 多层次风险控制，确保资金安全

#### 11.1 资金检查规则
```python
# ✅ 正确：资金充足性检查
async def check_fund_sufficiency(self, required_amount: float) -> bool:
    """检查资金充足性"""
    available_balance = await self.get_available_balance()
    safety_margin = 0.05  # 5%安全边际
    return available_balance >= required_amount * (1 + safety_margin)
```

#### 11.2 风险限额规则
- **单笔限额**: 不超过总资金的10%
- **日累计限额**: 不超过总资金的50%
- **最大持仓**: 不超过3个同时套利
- **止损阈值**: 单笔亏损不超过0.5%

### Rule 12: 安全防护规则
**强制要求**: API密钥安全，操作权限控制

#### 12.1 API密钥管理规则
```python
# ✅ 正确：安全的API密钥管理
from config.security import get_encrypted_api_key
api_key = get_encrypted_api_key("GATE_API_KEY")

# ❌ 错误：明文存储API密钥
api_key = "your_api_key_here"  # 禁止！必须加密存储
```

---

## 📊 监控与运维规则

### Rule 13: 日志记录规则
**强制要求**: 使用统一日志系统，8个专用日志文件

#### 13.1 日志分类规则
```python
# ✅ 正确：使用专用日志记录器
arbitrage_logger = get_logger("arbitrage")
execution_logger = get_logger("execution")
websocket_logger = get_logger("websocket")

arbitrage_logger.info(f"发现套利机会: {symbol}, 差价: {spread:.4f}")
execution_logger.info(f"执行订单: {order_id}, 状态: {status}")
```

#### 13.2 日志格式规则
- **时间戳**: YYYY-MM-DD HH:MM:SS.mmm毫秒精度
- **级别**: DEBUG/INFO/WARNING/ERROR/CRITICAL
- **模块**: [模块名.类名]精确定位
- **内容**: 结构化消息，便于分析

### Rule 14: 监控告警规则
**强制要求**: 实时监控系统状态，异常自动告警

#### 14.1 健康检查规则
```python
# ✅ 正确：系统健康检查
async def system_health_check(self) -> Dict:
    """系统健康检查"""
    health_status = {
        "websocket_connections": await self.check_websocket_health(),
        "exchange_connectivity": await self.check_exchange_connectivity(),
        "data_freshness": await self.check_data_freshness(),
        "execution_performance": await self.check_execution_performance()
    }
    return health_status
```

---

## 🚀 部署与维护规则

### Rule 15: 部署规则
**强制要求**: 标准化部署流程，环境一致性

#### 15.1 环境配置规则
- **开发环境**: 使用模拟数据，完整功能测试
- **测试环境**: 使用真实API，小额资金测试
- **生产环境**: 完整资金，实时监控

#### 15.2 版本管理规则
```python
# ✅ 正确：版本信息管理
VERSION = "2.1.0"
BUILD_DATE = "2025-07-31"
COMMIT_HASH = "abc123def"

def get_system_info() -> Dict:
    """获取系统版本信息"""
    return {
        "version": VERSION,
        "build_date": BUILD_DATE,
        "commit_hash": COMMIT_HASH
    }
```

### Rule 16: 维护规则
**强制要求**: 定期维护，持续优化

#### 16.1 定期维护任务
- **每日**: 检查系统日志，监控性能指标
- **每周**: 更新交易规则缓存，检查API限制
- **每月**: 系统性能评估，优化建议实施

#### 16.2 升级规则
- **向后兼容**: 新版本必须向后兼容
- **渐进升级**: 分模块逐步升级
- **回滚机制**: 升级失败自动回滚

---

## 📝 文档规则

### Rule 17: 代码文档规则
**强制要求**: 完整的代码文档，便于维护

#### 17.1 函数文档规则
```python
# ✅ 正确：完整的函数文档
async def calculate_arbitrage_profit(
    self,
    opening_spread: float,
    closing_spread: float,
    amount: float
) -> float:
    """
    计算套利利润

    Args:
        opening_spread: 开仓时的价差
        closing_spread: 平仓时的价差
        amount: 交易金额

    Returns:
        float: 预期利润金额

    Raises:
        ValueError: 当价差参数无效时

    Example:
        profit = await engine.calculate_arbitrage_profit(0.0025, -0.003, 1000.0)
    """
    if opening_spread <= 0:
        raise ValueError("开仓价差必须大于0")

    profit_rate = abs(opening_spread) + abs(closing_spread)
    return amount * profit_rate
```

### Rule 18: 系统文档规则
**强制要求**: 维护完整的系统文档

#### 18.1 必需文档清单
- **README.md**: 系统概览和快速开始
- **API文档**: 完整的API接口文档
- **配置文档**: 详细的配置参数说明
- **部署文档**: 标准化部署流程
- **故障排除**: 常见问题解决方案

---

## 🎯 核心实现要求

### Rule 19: 精度控制规则
**强制要求**: 使用Decimal进行高精度计算，避免浮点误差

#### 19.1 数值计算规则
```python
# ✅ 正确：使用Decimal高精度计算
from decimal import Decimal, getcontext
getcontext().prec = 12  # 12位精度

price = Decimal(str(raw_price))
amount = Decimal(str(raw_amount))
total = price * amount

# ❌ 错误：使用float计算
total = float(price) * float(amount)  # 禁止！存在精度误差
```

#### 19.2 价格格式化规则
```python
# ✅ 正确：统一价格格式化
from utils.price_formatter import format_price
formatted_price = format_price(price, symbol="BTC_USDT")

# ❌ 错误：手动格式化
formatted_price = f"{price:.8f}"  # 禁止！精度可能不正确
```

### Rule 20: 异步调用规则
**强制要求**: 正确处理异步调用，避免竞态条件

#### 20.1 异步并发控制规则
```python
# ✅ 正确：使用信号量控制并发
import asyncio
semaphore = asyncio.Semaphore(5)  # 最多5个并发请求

async def safe_api_call(self, exchange, method, params):
    async with semaphore:
        return await exchange.call_api(method, params)

# ❌ 错误：无限制并发
tasks = [exchange.call_api(method, params) for _ in range(100)]  # 禁止！
results = await asyncio.gather(*tasks)
```

#### 20.2 异步错误处理规则
```python
# ✅ 正确：异步错误处理
async def execute_with_retry(self, func, max_retries=3):
    """带重试的异步执行"""
    for attempt in range(max_retries):
        try:
            return await func()
        except (NetworkError, TimeoutError) as e:
            if attempt == max_retries - 1:
                raise
            await asyncio.sleep(2 ** attempt)  # 指数退避
```

### Rule 21: 数据一致性规则
**强制要求**: 确保跨交易所数据的时间和格式一致性

#### 21.1 时间同步规则
```python
# ✅ 正确：时间同步检查
async def check_time_sync(self):
    """检查时间同步"""
    local_time = time.time() * 1000
    for exchange_name, exchange in self.exchanges.items():
        server_time = await exchange.get_server_time()
        time_diff = abs(local_time - server_time)
        if time_diff > 1000:  # 1秒阈值
            self.logger.warning(f"{exchange_name}时间偏差: {time_diff}ms")
```

#### 21.2 数据格式统一规则
```python
# ✅ 正确：统一数据格式
from websocket.unified_data_formatter import UnifiedDataFormatter
formatter = UnifiedDataFormatter()

# 所有交易所数据统一格式化
unified_orderbook = formatter.format_orderbook(raw_data, exchange="gate")
```

---

## 🔍 质量保证规则

### Rule 22: 代码审查规则
**强制要求**: 所有代码变更必须通过严格审查

#### 22.1 审查检查清单
- **功能正确性**: 逻辑是否正确实现需求
- **性能影响**: 是否满足性能指标要求
- **安全性**: 是否存在安全漏洞
- **一致性**: 是否符合架构设计原则
- **测试覆盖**: 是否有充分的测试

#### 22.2 代码质量标准
```python
# ✅ 正确：符合质量标准的代码
class UnifiedOrderManager:
    """统一订单管理器 - 负责跨交易所订单协调"""

    def __init__(self, exchanges: Dict[str, Any], config: Dict):
        self.exchanges = exchanges
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self._order_cache = {}

    async def place_hedge_orders(
        self,
        spot_params: Dict,
        futures_params: Dict
    ) -> Tuple[str, str]:
        """
        下达对冲订单

        Args:
            spot_params: 现货订单参数
            futures_params: 期货订单参数

        Returns:
            Tuple[str, str]: (现货订单ID, 期货订单ID)

        Raises:
            OrderExecutionError: 订单执行失败
            HedgeQualityError: 对冲质量不足
        """
        # 验证参数
        self._validate_order_params(spot_params, futures_params)

        # 原子性执行
        try:
            spot_order_id, futures_order_id = await self._execute_atomic_orders(
                spot_params, futures_params
            )

            # 验证对冲质量
            hedge_quality = await self._verify_hedge_quality(
                spot_order_id, futures_order_id
            )

            if hedge_quality < 0.98:
                await self._cancel_orders([spot_order_id, futures_order_id])
                raise HedgeQualityError(f"对冲质量不足: {hedge_quality:.2%}")

            return spot_order_id, futures_order_id

        except Exception as e:
            self.logger.error(f"对冲订单执行失败: {e}")
            raise OrderExecutionError(f"对冲订单执行失败: {e}")
```

### Rule 23: 持续集成规则
**强制要求**: 自动化测试和部署流程

#### 23.1 自动化测试规则
- **单元测试**: 每次提交自动运行
- **集成测试**: 每日自动运行
- **性能测试**: 每周自动运行
- **安全测试**: 每月自动运行

#### 23.2 部署流程规则
```python
# ✅ 正确：自动化部署检查
def pre_deployment_check():
    """部署前检查"""
    checks = [
        check_all_tests_pass(),
        check_performance_benchmarks(),
        check_security_scan(),
        check_configuration_validity(),
        check_database_migration()
    ]

    for check_name, check_result in checks:
        if not check_result:
            raise DeploymentError(f"部署检查失败: {check_name}")
```

---

## 🎯 总结

### 核心原则
1. **统一性**: 使用31个核心统一模块，确保架构一致
2. **一致性**: 遵循18大一致性维度，确保零误差运行
3. **性能**: 满足毫秒级性能要求，>99.9%可用性
4. **安全**: 多层次风险控制，API密钥安全管理
5. **质量**: 机构级代码质量，完整测试覆盖
6. **可维护**: 清晰的代码结构，完整的文档体系

### 成功标准
- **功能完整**: 支持期货溢价套利全流程
- **性能达标**: 满足所有性能指标要求
- **质量保证**: 通过所有测试用例
- **安全可靠**: 零安全事故，稳定运行
- **易于维护**: 代码清晰，文档完整

### 关键技术指标
- **执行延迟**: <30ms机会发现，<100ms订单执行
- **数据一致性**: 100%原子快照，零时间差
- **对冲质量**: >98%精确对冲
- **系统可用性**: >99.9%运行时间
- **缓存命中率**: >95%缓存效率
- **API响应时间**: <1000ms最大延迟

**遵循这些专业开发规则，确保构建出世界级的期货溢价套利系统！**
