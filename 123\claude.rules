# Claude 期现套利系统开发规则 🚀
# 基于29个统一模块 + 18大一致性维度的世界级高频交易系统

## 实现最佳实践

### 0 — 目的

这些规则确保期现套利系统的**通用性、一致性、高性能、精准性**。
**MUST** 规则由CI强制执行；**SHOULD** 规则强烈推荐遵循。

**🔥 核心原则**: 18大一致性维度 + 29个统一模块 + 毫秒级性能 + 12位精度

---

### 1 — 编码前 (通用系统设计)

- **BP-1 (MUST)** 向用户询问澄清问题，特别是一致性要求和性能指标。
- **BP-2 (MUST)** 检查是否可以复用29个统一模块中的现有实现，避免重复开发。
- **BP-3 (MUST)** 对于新功能，必须评估对18大一致性维度的影响。
- **BP-4 (SHOULD)** 优先选择通用解决方案，支持任意代币的零硬编码设计。

---

### 2 — 编码时 (一致性优先)

- **C-1 (MUST)** 遵循TDD：搭建存根 -> 编写失败测试 -> 实现功能。
- **C-2 (MUST)** 强制使用29个统一模块，禁止重复实现：
  ```python
  # ✅ 正确 - 使用统一模块
  from core.unified_order_spread_calculator import get_order_spread_calculator
  from websocket.unified_timestamp_processor import get_synced_timestamp
  from core.universal_token_system import get_universal_token_system

  # ❌ 错误 - 重复实现
  def my_custom_spread_calculator():  # 禁止
  def calculate_timestamp():          # 禁止
  ```
- **C-3 (MUST)** 对交易ID使用品牌化类型，确保类型安全：
  ```python
  # ✅ 正确
  from typing import NewType
  OrderId = NewType('OrderId', str)
  SymbolId = NewType('SymbolId', str)
  ExchangeName = NewType('ExchangeName', str)

  # ❌ 错误
  order_id: str  # 类型不够明确，可能混淆
  ```
- **C-4 (MUST)** 所有时间戳必须通过统一时间戳处理器，确保时间一致性：
  ```python
  # ✅ 正确 - 时间戳一致性
  from websocket.unified_timestamp_processor import get_synced_timestamp
  timestamp = get_synced_timestamp("gate", data)

  # ❌ 错误 - 破坏时间一致性
  timestamp = int(time.time() * 1000)  # 禁止直接使用
  ```
- **C-5 (MUST)** 所有数值计算必须使用Decimal，确保12位精度：
  ```python
  # ✅ 正确 - 高精度计算
  from decimal import Decimal, ROUND_HALF_UP
  spread = Decimal(str(futures_price - spot_price)) / Decimal(str(spot_price))

  # ❌ 错误 - 精度丢失
  spread = (futures_price - spot_price) / spot_price  # 禁止float
  ```
- **C-6 (MUST)** 所有数据格式化必须通过统一格式化器：
  ```python
  # ✅ 正确 - 数据格式一致性
  from websocket.unified_data_formatter import format_orderbook_data
  formatted = format_orderbook_data(asks, bids, symbol, exchange)

  # ❌ 错误 - 格式不一致
  formatted = {"asks": asks, "bids": bids}  # 禁止自定义格式
  ```
- **C-7 (SHOULD NOT)** 除关键注意事项外不添加注释；依赖自解释代码。
- **C-8 (SHOULD)** 默认使用dataclass；仅在需要继承或复杂行为时使用class。

---

### 3 — 测试 (精准性验证)

- **T-1 (MUST)** 对于简单函数，在源文件同目录下的`*_test.py`中放置单元测试。
- **T-2 (MUST)** 对于任何套利逻辑变更，在`tests/integration/`中添加/扩展集成测试。
- **T-3 (MUST)** 始终将纯逻辑单元测试与涉及交易所API的集成测试分离。
- **T-4 (MUST)** 所有一致性相关测试必须验证18大一致性维度：
  ```python
  # ✅ 正确 - 一致性验证测试
  def test_timestamp_consistency_across_exchanges():
      """验证跨交易所时间戳一致性"""
      gate_ts = get_synced_timestamp("gate", gate_data)
      bybit_ts = get_synced_timestamp("bybit", bybit_data)
      okx_ts = get_synced_timestamp("okx", okx_data)

      # 时间戳差异必须<200ms
      assert abs(gate_ts - bybit_ts) < 200
      assert abs(bybit_ts - okx_ts) < 200
  ```
- **T-5 (MUST)** 所有精度测试必须使用Decimal和hypothesis属性测试：
  ```python
  # ✅ 正确 - 精度属性测试
  from hypothesis import given, strategies as st
  from decimal import Decimal

  @given(
      futures_price=st.decimals(min_value=Decimal('1000'), max_value=Decimal('100000')),
      spot_price=st.decimals(min_value=Decimal('1000'), max_value=Decimal('100000'))
  )
  def test_spread_calculation_precision_properties(futures_price, spot_price):
      """测试差价计算精度属性"""
      spread = calculate_spread_with_decimal(futures_price, spot_price)

      # 精度必须保持12位小数
      assert len(str(spread).split('.')[-1]) <= 12
      # 计算必须可逆
      reconstructed = spot_price * (1 + spread)
      assert abs(reconstructed - futures_price) < Decimal('0.000000000001')
  ```
- **T-6 (MUST)** 性能测试必须验证毫秒级要求：
  ```python
  # ✅ 正确 - 性能测试
  def test_opportunity_discovery_performance():
      """验证机会发现<30ms性能要求"""
      start_time = time.time()
      opportunity = scanner.scan_opportunities()
      execution_time = (time.time() - start_time) * 1000

      assert execution_time < 30, f"机会发现耗时{execution_time:.1f}ms > 30ms"
  ```

---

### 4 — 通用系统架构

- **U-1 (MUST)** 必须使用UniversalTokenSystem，支持任意代币，零硬编码：
  ```python
  # ✅ 正确 - 通用代币系统
  from core.universal_token_system import get_universal_token_system
  token_system = get_universal_token_system()
  if token_system.is_symbol_supported(symbol):
      # 处理逻辑

  # ❌ 错误 - 硬编码代币
  if symbol in ["BTC-USDT", "ETH-USDT"]:  # 禁止硬编码
  ```
- **U-2 (MUST)** 所有交易所操作必须通过统一接口，确保三交易所完全一致：
  ```python
  # ✅ 正确 - 统一交易所接口
  from exchanges.exchanges_base import BaseExchange
  result = await exchange.place_order(symbol, side, order_type, amount)

  # ❌ 错误 - 直接调用交易所特定方法
  result = await gate_client.spot_order_create()  # 禁止
  ```
- **U-3 (MUST)** 使用原子快照技术确保跨交易所数据一致性：
  ```python
  # ✅ 正确 - 原子快照
  snapshot = calculator._create_atomic_snapshot(spot_data, futures_data, "opening")

  # ❌ 错误 - 非原子数据使用
  spread = (futures_data['price'] - spot_data['price']) / spot_data['price']
  ```

---

### 5 — 高性能要求

- **P-1 (MUST)** 机会发现必须<30ms，订单执行必须<100ms。
- **P-2 (MUST)** 使用6大缓存系统，实现零延迟验证：
  ```python
  # ✅ 正确 - 缓存优先
  from utils.cache_monitor import log_trading_rules_hit
  rule = preloader.get_trading_rule(exchange, symbol, market_type)  # 缓存

  # ❌ 错误 - 实时API调用
  rule = await exchange.get_trading_rules()  # 禁止实时调用
  ```
- **P-3 (MUST)** 使用二分查找算法处理30档深度，确保O(log n)复杂度：
  ```python
  # ✅ 正确 - 二分查找
  execution_level = calculator.find_optimal_execution_level(cum_table, amount)

  # ❌ 错误 - 线性搜索
  for level in depth_levels:  # 禁止O(n)算法
  ```
- **P-4 (MUST)** 所有异步操作必须并行执行，避免串行等待：
  ```python
  # ✅ 正确 - 并行执行
  spot_task = asyncio.create_task(execute_spot_order())
  futures_task = asyncio.create_task(execute_futures_order())
  results = await asyncio.gather(spot_task, futures_task)

  # ❌ 错误 - 串行执行
  spot_result = await execute_spot_order()
  futures_result = await execute_futures_order()  # 禁止串行
  ```

---

### 6 — 精准性保证

- **A-1 (MUST)** 所有金融计算必须使用Decimal，保持12位精度：
  ```python
  # ✅ 正确 - 12位精度
  from decimal import Decimal, ROUND_HALF_UP
  spread = Decimal(str(executable_spread)).quantize(
      Decimal('0.000000000001'), rounding=ROUND_HALF_UP
  )

  # ❌ 错误 - 精度丢失
  spread = round(executable_spread, 8)  # 禁止float精度
  ```
- **A-2 (MUST)** 所有数量格式化必须使用截取而非四舍五入：
  ```python
  # ✅ 正确 - 精准截取
  multiplier = 10 ** precision
  truncated = int(amount * multiplier) / multiplier

  # ❌ 错误 - 四舍五入可能超出限制
  rounded = round(amount, precision)  # 禁止四舍五入
  ```
- **A-3 (MUST)** 对冲质量必须达到98%，使用智能协调算法：
  ```python
  # ✅ 正确 - 对冲质量验证
  hedge_quality = calculate_hedge_quality(spot_amount, futures_amount)
  assert hedge_quality >= 0.98, f"对冲质量{hedge_quality:.4f} < 98%"
  ```

---

### 7 — 一致性维度强制要求

- **CON-1 (MUST)** 时间戳一致性 - 全局10ms边界对齐，跨交易所<200ms差异。
- **CON-2 (MUST)** 差价计算一致性 - 统一公式(期货-现货)/现货，12位精度。
- **CON-3 (MUST)** 数据源一致性 - WebSocket优先，统一格式化，质量评分>80。
- **CON-4 (MUST)** 交易执行一致性 - 统一订单格式，98%对冲质量，原子执行。
- **CON-5 (MUST)** 缓存系统一致性 - 6大缓存统一TTL，零延迟验证。
- **CON-6 (MUST)** 网络连接一致性 - 统一重连机制，>99%连接稳定性。
- **CON-7 (MUST)** 配置管理一致性 - 统一.env配置，零硬编码。
- **CON-8 (MUST)** 日志记录一致性 - 统一格式，结构化日志。
- **CON-9 (MUST)** 异常处理一致性 - 统一异常类型，标准化错误码。
- **CON-10 (MUST)** API接口一致性 - 统一方法签名，标准化返回值。
- **CON-11 (MUST)** 订单管理一致性 - 统一订单ID格式，状态枚举。
- **CON-12 (MUST)** 资金管理一致性 - 统一余额查询，账户类型标准化。
- **CON-13 (MUST)** 风险控制一致性 - 统一风险参数，标准化限额。
- **CON-14 (MUST)** 监控告警一致性 - 统一监控指标，标准化告警。
- **CON-15 (MUST)** 异步处理一致性 - 统一异步模式，标准化并发控制。
- **CON-16 (MUST)** WebSocket连接一致性 - 统一连接池，标准化心跳。
- **CON-17 (MUST)** 安全验证一致性 - 统一签名算法，标准化权限。
- **CON-18 (MUST)** 算法实现一致性 - 统一算法接口，标准化参数。

---

### 8 — 工具门控 (性能验证)

- **G-1 (MUST)** `black --check .` 通过（代码格式化）。
- **G-2 (MUST)** `mypy .` 通过（类型检查）。
- **G-3 (MUST)** `pytest tests/` 通过（所有测试）。
- **G-4 (MUST)** 性能测试：机会发现<30ms，订单执行<100ms，对冲质量>98%。
- **G-5 (MUST)** 一致性测试：18大维度全部通过，29个统一模块集成测试通过。
- **G-6 (MUST)** 精度测试：12位Decimal精度验证，零浮点误差。

---

### 9 - Git (一致性提交)

- **GH-1 (MUST)** 使用约定式提交格式编写提交消息：https://www.conventionalcommits.org/en/v1.0.0
- **GH-2 (SHOULD NOT)** 在提交消息中提及Claude或Anthropic。
- **GH-3 (MUST)** 提交消息必须标明影响的一致性维度：
  ```
  feat(consistency): 优化时间戳一致性处理 [CON-1]
  fix(precision): 修复差价计算精度问题 [CON-2,A-1]
  perf(cache): 提升缓存系统性能 [CON-5,P-2]
  ```

---

## 编写函数最佳实践 (一致性和性能导向)

评估你实现的函数是否符合期现套利系统标准时，使用此检查清单：

### 🔥 一致性检查清单
1. **统一模块依赖**: 函数是否使用了29个统一模块中的现有实现？避免重复开发。
2. **时间戳一致性**: 是否通过`get_synced_timestamp()`获取时间戳？禁止直接使用`time.time()`。
3. **精度一致性**: 是否使用Decimal进行金融计算？禁止float运算导致精度丢失。
4. **数据格式一致性**: 是否通过统一格式化器处理数据？确保跨交易所格式统一。
5. **接口一致性**: 是否遵循BaseExchange统一接口？确保三交易所完全一致。

### 🚀 性能检查清单
6. **缓存优先**: 是否优先使用缓存而非实时API调用？确保零延迟验证。
7. **并行执行**: 是否使用asyncio.gather()并行执行？避免串行等待。
8. **算法复杂度**: 是否使用O(log n)二分查找而非O(n)线性搜索？
9. **内存效率**: 是否避免不必要的深拷贝和大对象创建？

### 🎯 通用性检查清单
10. **零硬编码**: 是否通过UniversalTokenSystem支持任意代币？禁止硬编码代币列表。
11. **配置驱动**: 是否通过.env配置而非硬编码参数？确保系统可配置性。
12. **异常安全**: 是否有完善的异常处理和降级机制？确保系统稳定性。

### 🔍 代码质量检查清单
13. 你能阅读函数并诚实地轻松理解它在做什么吗？如果是，则在此停止。
14. 函数是否具有非常高的圈复杂度？如果是，考虑拆分或使用状态机。
15. 是否有任何常见的数据结构和算法可以使此函数更容易理解？
16. 函数中是否有任何未使用的参数？
17. 是否有任何不必要的类型转换可以移到函数参数中？
18. 函数是否可以在不模拟核心功能的情况下轻松测试？
19. 头脑风暴3个更好的函数名，确保与29个统一模块命名一致。

**🚨 重要**: 除非有令人信服的需要，否则不应该重构出单独的函数：
  - 重构的函数在≥2个地方使用（符合统一模块原则）
  - 重构的函数易于单元测试，而原始函数不是
  - 原始函数极难理解，影响一致性维护

## 编写测试最佳实践 (一致性和精准性验证)

评估你实现的测试是否符合期现套利系统标准时，使用此检查清单：

### 🔥 一致性测试要求
1. **18大一致性维度验证**: 每个测试必须验证相关的一致性维度，使用CON-X标记。
2. **29个统一模块集成**: 测试必须验证统一模块的正确集成和接口一致性。
3. **跨交易所一致性**: 测试必须验证Gate.io、Bybit、OKX三交易所的行为一致性。

### 🎯 精准性测试要求
4. **12位精度验证**: 所有金融计算测试必须使用Decimal和hypothesis属性测试：
```python
from hypothesis import given, strategies as st
from decimal import Decimal

@given(
    futures_price=st.decimals(min_value=Decimal('1000'), max_value=Decimal('100000')),
    spot_price=st.decimals(min_value=Decimal('1000'), max_value=Decimal('100000'))
)
def test_spread_calculation_precision_invariants(futures_price, spot_price):
    """测试差价计算精度不变量 [CON-2,A-1]"""
    calculator = get_order_spread_calculator()
    spread = calculator.calculate_executable_spread(
        futures_price, spot_price, "opening"
    )

    # 精度不变量：12位小数精度
    spread_str = str(spread)
    if '.' in spread_str:
        decimal_places = len(spread_str.split('.')[-1])
        assert decimal_places <= 12

    # 数学不变量：可逆性
    reconstructed = spot_price * (Decimal('1') + spread)
    assert abs(reconstructed - futures_price) < Decimal('0.000000000001')
```

### 🚀 性能测试要求
5. **毫秒级性能验证**: 所有性能关键路径必须有性能测试：
```python
def test_opportunity_discovery_performance_requirement():
    """验证机会发现<30ms性能要求 [P-1]"""
    scanner = OpportunityScanner()

    # 预热缓存
    await scanner.preheat_caches()

    # 性能测试
    start_time = time.time()
    opportunities = await scanner.scan_opportunities()
    execution_time = (time.time() - start_time) * 1000

    assert execution_time < 30, f"机会发现耗时{execution_time:.1f}ms > 30ms"
```

### 📊 通用性测试要求
6. **零硬编码验证**: 测试必须验证系统对任意代币的支持：
```python
@pytest.mark.parametrize("symbol", [
    "BTC-USDT", "ETH-USDT", "LINK-USDT", "DOGE-USDT"  # 动态从配置获取
])
def test_universal_token_support(symbol):
    """验证通用代币系统支持 [U-1]"""
    token_system = get_universal_token_system()
    assert token_system.is_symbol_supported(symbol)

    # 验证所有交易所都支持
    for exchange in ["gate", "bybit", "okx"]:
        assert token_system.validate_symbol_for_exchange(symbol, exchange, "spot")
        assert token_system.validate_symbol_for_exchange(symbol, exchange, "futures")
```

### 📋 标准测试实践
7. 应该参数化输入；永远不要在测试中直接嵌入未解释的字面量。
8. 除非测试可能因真实缺陷而失败，否则不应添加测试。禁止平凡断言。
9. 应该确保测试描述准确说明最终assert验证的内容和相关一致性维度。
10. 应该将结果与独立的、预计算的期望或领域属性进行比较。
11. 应该遵循与生产代码相同的lint、类型安全和样式规则。
12. 函数的单元测试应该在`class TestFunctionName:`下分组。
13. 测试浮点数比较时使用`pytest.approx(...)`，但金融计算必须使用Decimal精确比较。
14. 始终使用强断言而非弱断言，例如`assert x == expected_value`。
15. 应该测试边缘情况、现实输入、意外输入和值边界。
16. 不应该测试类型检查器捕获的条件。

## 代码组织 (29个统一模块架构)

### 🧠 Core目录 (16个统一模块)
- `core/arbitrage_engine.py` - 套利引擎主控制器
- `core/opportunity_scanner.py` - 机会扫描器 (动态扫描)
- `core/execution_engine.py` - 执行引擎 (通用执行逻辑)
- `core/unified_order_spread_calculator.py` - 🔥统一Order差价计算器 (核心)
- `core/universal_token_system.py` - 通用代币系统 (零硬编码)
- `core/unified_opening_manager.py` - 统一开仓管理器
- `core/unified_closing_manager.py` - 统一平仓管理器
- `core/unified_balance_manager.py` - 统一余额管理器
- `core/unified_depth_analyzer.py` - 统一深度分析器
- `core/convergence_monitor.py` - 价差趋同监控器
- `core/execution_params_preparer.py` - 执行参数准备器
- `core/order_pairing_manager.py` - 订单配对管理器
- `core/trading_system_initializer.py` - 交易系统初始化器
- `core/unified_http_session_manager.py` - 统一HTTP会话管理器
- `core/unified_leverage_manager.py` - 统一杠杆管理器
- `core/trading_rules_preloader.py` - 交易规则预加载器

### 🏪 Exchanges目录 (4个统一模块)
- `exchanges/exchanges_base.py` - 交易所基类 (统一接口)
- `exchanges/gate_exchange.py` - Gate.io实现
- `exchanges/bybit_exchange.py` - Bybit实现
- `exchanges/okx_exchange.py` - OKX实现

### 🌐 WebSocket目录 (4个统一模块)
- `websocket/orderbook_validator.py` - 统一订单簿验证器
- `websocket/unified_data_formatter.py` - 统一数据格式化器
- `websocket/unified_timestamp_processor.py` - 🔥统一时间戳处理器 (核心)
- `websocket/unified_connection_pool_manager.py` - 统一连接池管理器

### 🛠️ Utils目录 (4个统一模块)
- `utils/cache_monitor.py` - 统一缓存监控系统 (6大缓存)
- `utils/min_order_detector.py` - 动态最小金额检测器
- `utils/hedge_calculator.py` - 对冲计算器
- `utils/margin_calculator.py` - 保证金计算器

### 📁 Config目录 (1个统一模块)
- `config/unified_network_config_manager.py` - 统一网络配置管理器

### 🧪 Tests目录 (一致性和性能验证)
- `tests/unit/` - 单元测试 (29个统一模块测试)
- `tests/integration/` - 集成测试 (18大一致性维度验证)
- `tests/performance/` - 性能测试 (<30ms机会发现, <100ms执行)
- `tests/consistency/` - 一致性测试 (跨交易所一致性验证)

## 记住快捷方式 (一致性和性能导向)

记住以下快捷方式，用户可能随时调用。

### QNEW

当我输入"qnew"时，这意味着：

```
理解CLAUDE.RULES中列出的所有最佳实践，特别是：
- 18大一致性维度强制要求 (CON-1到CON-18)
- 29个统一模块架构原则
- 毫秒级性能要求 (<30ms机会发现, <100ms执行)
- 12位精度Decimal计算标准
你的代码应该始终遵循这些最佳实践。
```

### QPLAN
当我输入"qplan"时，这意味着：
```
分析代码库的类似部分，确定你的计划：
- 检查29个统一模块中是否有可复用实现
- 评估对18大一致性维度的影响
- 确保性能要求 (<30ms发现, <100ms执行, >98%对冲质量)
- 验证通用性 (支持任意代币，零硬编码)
- 与代码库其余部分保持一致
- 引入最少的变更，重用现有统一模块
```

### QCODE

当我输入"qcode"时，这意味着：

```
实现你的计划并确保符合期现套利系统标准：
1. 使用29个统一模块，避免重复实现
2. 确保18大一致性维度合规
3. 运行性能测试验证<30ms和<100ms要求
4. 运行一致性测试验证跨交易所一致性
5. 运行精度测试验证12位Decimal精度
6. 始终运行 `black . && mypy . && pytest tests/`
7. 验证对冲质量>98%和零硬编码要求
```

### QCHECK

当我输入"qcheck"时，这意味着：

```
你是一个怀疑的高级软件工程师，专注于期现套利系统的一致性和性能。
对你引入的每个主要代码变更执行此分析（跳过小变更）：

1. 一致性检查：验证18大一致性维度合规性 (CON-1到CON-18)
2. 统一模块检查：确认使用29个统一模块，避免重复实现
3. 性能检查：验证<30ms机会发现和<100ms执行要求
4. 精度检查：确认12位Decimal精度和零浮点误差
5. 通用性检查：验证零硬编码和任意代币支持
6. CLAUDE.RULES函数最佳实践检查清单
7. CLAUDE.RULES测试最佳实践检查清单
```

### QCHECKF

当我输入"qcheckf"时，这意味着：

```
你是一个怀疑的高级软件工程师。
对你添加或编辑的每个主要函数执行此分析（跳过小变更）：

1. 统一模块依赖检查：是否复用现有统一模块？
2. 时间戳一致性检查：是否使用get_synced_timestamp()？
3. 精度一致性检查：是否使用Decimal进行金融计算？
4. 性能检查：是否满足毫秒级性能要求？
5. CLAUDE.RULES函数最佳实践检查清单
```

### QCHECKT

当我输入"qcheckt"时，这意味着：

```
你是一个怀疑的高级软件工程师。
对你添加或编辑的每个主要测试执行此分析（跳过小变更）：

1. 一致性测试检查：是否验证相关的一致性维度？
2. 精度测试检查：是否使用hypothesis和Decimal验证精度？
3. 性能测试检查：是否验证毫秒级性能要求？
4. 通用性测试检查：是否验证任意代币支持？
5. CLAUDE.RULES测试最佳实践检查清单
```

### QUX

当我输入"qux"时，这意味着：

```
想象你是期现套利系统的专业交易员测试者。
输出你将测试的场景的综合列表，按最高优先级排序：

高优先级场景：
1. 正常套利流程 (期货溢价+0.25% → 现货溢价-0.3%)
2. 18大一致性维度验证 (时间戳、差价计算、数据源等)
3. 29个统一模块集成测试
4. 毫秒级性能验证 (<30ms发现, <100ms执行)
5. 12位精度计算验证
6. 98%对冲质量验证
7. 原子快照技术验证

中优先级场景：
8. 异常市场条件 (极端波动、流动性不足)
9. 网络故障恢复 (断线重连、API限制)
10. 缓存系统压力测试
11. 跨交易所数据同步测试

低优先级场景：
12. 边界条件测试
13. 配置参数验证
14. 日志记录完整性
```

### QGIT

当我输入"qgit"时，这意味着：

```
将所有变更添加到暂存区，创建提交，并推送到远程。

遵循此检查清单编写提交消息：
- 使用约定式提交格式：https://www.conventionalcommits.org/en/v1.0.0
- 不在提交消息中提及Claude或Anthropic
- 标明影响的一致性维度：[CON-X] 或性能指标：[P-X] 或精度要求：[A-X]
- 提交消息结构：
  <type>[optional scope]: <description> [consistency-tags]

示例：
feat(core): 优化统一差价计算器性能 [CON-2,P-1,A-1]
fix(websocket): 修复时间戳一致性问题 [CON-1]
perf(cache): 提升缓存系统响应速度 [CON-5,P-2]
test(integration): 添加跨交易所一致性测试 [CON-3,CON-4]
```

### QARB

当我输入"qarb"时，这意味着：

```
专门针对期现套利系统的全面检查：

🔥 一致性维度检查 (18大维度)：
1. 时间戳一致性 [CON-1]: 10ms边界对齐，<200ms跨交易所差异
2. 差价计算一致性 [CON-2]: 统一公式，12位精度
3. 数据源一致性 [CON-3]: WebSocket优先，质量评分>80
4. 交易执行一致性 [CON-4]: 98%对冲质量，原子执行
5. 缓存系统一致性 [CON-5]: 6大缓存，零延迟验证
6. 网络连接一致性 [CON-6]: >99%连接稳定性
7. 配置管理一致性 [CON-7]: 零硬编码，.env驱动
8. 其余11个一致性维度验证...

🚀 性能指标检查：
1. 机会发现<30ms [P-1]
2. 订单执行<100ms [P-1]
3. 对冲质量>98% [A-3]
4. 缓存命中率>95% [P-2]

🎯 统一模块检查 (29个模块)：
1. 是否使用统一Order差价计算器？
2. 是否使用统一时间戳处理器？
3. 是否使用通用代币系统？
4. 是否使用统一数据格式化器？
5. 其余25个统一模块集成验证...

🔍 精准性检查：
1. 是否使用Decimal进行金融计算？
2. 是否使用截取而非四舍五入？
3. 是否实现原子快照技术？
4. 是否正确处理异步调用和精度问题？
```