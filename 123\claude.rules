# Claude 期现套利系统开发规则 🚀

## 实现最佳实践

### 0 — 目的

这些规则确保期现套利系统的可维护性、安全性和开发效率。
**MUST** 规则由CI强制执行；**SHOULD** 规则强烈推荐遵循。

---

### 1 — 编码前

- **BP-1 (MUST)** 向用户询问澄清问题，特别是交易参数和风险控制要求。
- **BP-2 (SHOULD)** 对复杂的套利逻辑起草并确认实现方案。
- **BP-3 (SHOULD)** 如果存在≥2种实现方案，列出明确的优缺点，优先选择性能最优方案。

---

### 2 — 编码时

- **C-1 (MUST)** 遵循TDD：搭建存根 -> 编写失败测试 -> 实现功能。
- **C-2 (MUST)** 使用现有的金融交易领域词汇命名函数，保持一致性。
- **C-3 (SHOULD NOT)** 当简单可测试函数足够时，不要引入类。
- **C-4 (SHOULD)** 优先使用简单、可组合、可测试的函数。
- **C-5 (MUST)** 对交易ID使用品牌化类型
  ```python
  # ✅ 正确
  from typing import NewType
  OrderId = NewType('OrderId', str)
  SymbolId = NewType('SymbolId', str)

  # ❌ 错误
  order_id: str  # 类型不够明确
  ```
- **C-6 (MUST)** 使用统一模块导入，避免重复实现。
  ```python
  # ✅ 正确
  from core.unified_order_spread_calculator import get_order_spread_calculator

  # ❌ 错误
  from some_custom_module import my_spread_calculator
  ```
- **C-7 (SHOULD NOT)** 除关键注意事项外不添加注释；依赖自解释代码。
- **C-8 (SHOULD)** 默认使用dataclass；仅在需要继承或复杂行为时使用class。
- **C-9 (SHOULD NOT)** 除非函数将在其他地方重用、是单元测试不可测试逻辑的唯一方法，或大幅提高不透明块的可读性，否则不要提取新函数。

---

### 3 — 测试

- **T-1 (MUST)** 对于简单函数，在源文件同目录下的`*_test.py`中放置单元测试。
- **T-2 (MUST)** 对于任何套利逻辑变更，在`tests/integration/`中添加/扩展集成测试。
- **T-3 (MUST)** 始终将纯逻辑单元测试与涉及交易所API的集成测试分离。
- **T-4 (SHOULD)** 优先使用集成测试而非大量模拟。
- **T-5 (SHOULD)** 彻底单元测试复杂的套利算法。
- **T-6 (SHOULD)** 如果可能，在一个断言中测试整个结构
  ```python
  # ✅ 正确
  assert result == expected_arbitrage_result

  # ❌ 错误
  assert len(result.orders) == 2
  assert result.orders[0].side == "buy"
  assert result.orders[1].side == "sell"
  ```

---

### 4 — 交易所集成

- **E-1 (MUST)** 所有交易所API调用必须通过统一接口，支持Gate.io、Bybit、OKX。
- **E-2 (SHOULD)** 在`exchanges/`目录中覆盖不正确的交易所响应类型。例如自动生成的类型显示不正确的价格精度 - 手动覆盖为正确的Decimal类型。
- **E-3 (MUST)** 使用原子快照技术确保跨交易所数据一致性。

---

### 5 — 代码组织

- **O-1 (MUST)** 仅当≥2个模块使用时，才将代码放在`core/`中。
- **O-2 (MUST)** 套利相关代码放在`arbitrage/`，WebSocket代码放在`websocket/`，工具函数放在`utils/`。

---

### 6 — 工具门控

- **G-1 (MUST)** `black --check .` 通过（代码格式化）。
- **G-2 (MUST)** `mypy .` 通过（类型检查）。
- **G-3 (MUST)** `pytest tests/` 通过（所有测试）。
- **G-4 (MUST)** 性能测试：机会发现<30ms，订单执行<100ms。

---

### 7 - Git

- **GH-1 (MUST)** 使用约定式提交格式编写提交消息：https://www.conventionalcommits.org/en/v1.0.0
- **GH-2 (SHOULD NOT)** 在提交消息中提及Claude或Anthropic。
- **GH-3 (SHOULD)** 使用中文描述套利相关的业务逻辑变更。

---

## 编写函数最佳实践

评估你实现的函数是否良好时，使用此检查清单：

1. 你能阅读函数并诚实地轻松理解它在做什么吗？如果是，则在此停止。
2. 函数是否具有非常高的圈复杂度？（独立路径数，或在很多情况下，if-else嵌套数作为代理）。如果是，那么它可能有问题。
3. 是否有任何常见的数据结构和算法可以使此函数更容易理解和更健壮？解析器、树、栈/队列等。
4. 函数中是否有任何未使用的参数？
5. 是否有任何不必要的类型转换可以移到函数参数中？
6. 函数是否可以在不模拟核心功能（例如交易所API调用、缓存等）的情况下轻松测试？如果不能，此函数是否可以作为集成测试的一部分进行测试？
7. 它是否有任何隐藏的未测试依赖项或任何可以分解到参数中的值？只关心可能实际改变或影响函数的非平凡依赖项。
8. 头脑风暴3个更好的函数名，看看当前名称是否最佳，与代码库其余部分一致。

重要：除非有令人信服的需要，否则你不应该重构出单独的函数，例如：
  - 重构的函数在多个地方使用
  - 重构的函数易于单元测试，而原始函数不是，并且你无法以任何其他方式测试它
  - 原始函数极难理解，你需要到处添加注释来解释它

## 编写测试最佳实践

评估你实现的测试是否良好时，使用此检查清单：

1. 应该参数化输入；永远不要在测试中直接嵌入未解释的字面量，如42或"BTC"。
2. 除非测试可能因真实缺陷而失败，否则不应添加测试。禁止平凡断言（例如，assert 2 == 2）。
3. 应该确保测试描述准确说明最终assert验证的内容。如果措辞和断言不一致，重命名或重写。
4. 应该将结果与独立的、预计算的期望或领域属性进行比较，永远不要与重用为预言的函数输出进行比较。
5. 应该遵循与生产代码相同的lint、类型安全和样式规则（black、mypy、严格类型）。
6. 应该表达不变量或公理（例如，套利利润的数学属性、对冲质量要求）而不是单个硬编码案例。使用`hypothesis`库，例如：
```python
from hypothesis import given, strategies as st
import pytest
from decimal import Decimal

@given(
    opening_spread=st.decimals(min_value=Decimal('0.001'), max_value=Decimal('0.05')),
    closing_spread=st.decimals(min_value=Decimal('-0.05'), max_value=Decimal('-0.001')),
    amount=st.decimals(min_value=Decimal('100'), max_value=Decimal('10000'))
)
def test_arbitrage_profit_calculation_properties(opening_spread, closing_spread, amount):
    """测试套利利润计算的数学属性"""
    profit = calculate_arbitrage_profit(opening_spread, closing_spread, amount)

    # 利润应该为正（套利的基本属性）
    assert profit > 0

    # 利润应该与金额成正比（线性属性）
    double_profit = calculate_arbitrage_profit(opening_spread, closing_spread, amount * 2)
    assert abs(double_profit - profit * 2) < Decimal('0.0001')
```

7. 函数的单元测试应该在`class TestFunctionName:`下分组。
8. 测试可以是任何值的参数时使用`pytest.approx(...)`（例如浮点数比较）。
9. 始终使用强断言而非弱断言，例如`assert x == expected_value`而不是`assert x >= min_value`。
10. 应该测试边缘情况、现实输入、意外输入和值边界。
11. 不应该测试类型检查器捕获的条件。

## 代码组织

- `core/` - 核心套利引擎
  - `core/arbitrage_engine.py` - 主套利引擎
  - `core/opportunity_scanner.py` - 机会扫描器
  - `core/execution_engine.py` - 执行引擎
  - `core/unified_*.py` - 统一模块系列
- `exchanges/` - 交易所集成
  - `exchanges/gate/` - Gate.io集成
  - `exchanges/bybit/` - Bybit集成
  - `exchanges/okx/` - OKX集成
- `websocket/` - WebSocket连接管理
- `utils/` - 共享工具和缓存
- `config/` - 配置管理
- `tests/` - 测试套件
  - `tests/unit/` - 单元测试
  - `tests/integration/` - 集成测试
  - `tests/performance/` - 性能测试

## 记住快捷方式

记住以下快捷方式，用户可能随时调用。

### QNEW

当我输入"qnew"时，这意味着：

```
理解CLAUDE.RULES中列出的所有最佳实践。
你的代码应该始终遵循这些最佳实践。
```

### QPLAN
当我输入"qplan"时，这意味着：
```
分析代码库的类似部分，确定你的计划：
- 与代码库其余部分一致
- 引入最少的变更
- 重用现有代码
- 符合期现套利系统的架构原则
```

## QCODE

当我输入"qcode"时，这意味着：

```
实现你的计划并确保新测试通过。
始终运行测试以确保你没有破坏其他任何东西。
始终在新创建的文件上运行`black`以确保标准格式。
始终运行`mypy . && pytest tests/`以确保类型检查和测试通过。
```

### QCHECK

当我输入"qcheck"时，这意味着：

```
你是一个怀疑的高级软件工程师。
对你引入的每个主要代码变更执行此分析（跳过小变更）：

1. CLAUDE.RULES检查清单编写函数最佳实践。
2. CLAUDE.RULES检查清单编写测试最佳实践。
3. CLAUDE.RULES检查清单实现最佳实践。
```

### QCHECKF

当我输入"qcheckf"时，这意味着：

```
你是一个怀疑的高级软件工程师。
对你添加或编辑的每个主要函数执行此分析（跳过小变更）：

1. CLAUDE.RULES检查清单编写函数最佳实践。
```

### QCHECKT

当我输入"qcheckt"时，这意味着：

```
你是一个怀疑的高级软件工程师。
对你添加或编辑的每个主要测试执行此分析（跳过小变更）：

1. CLAUDE.RULES检查清单编写测试最佳实践。
```

### QUX

当我输入"qux"时，这意味着：

```
想象你是你实现的套利功能的人工交易员测试者。
输出你将测试的场景的综合列表，按最高优先级排序。
包括：正常套利流程、异常市场条件、网络故障、API限制等。
```

### QGIT

当我输入"qgit"时，这意味着：

```
将所有变更添加到暂存区，创建提交，并推送到远程。

遵循此检查清单编写提交消息：
- 应该使用约定式提交格式：https://www.conventionalcommits.org/en/v1.0.0
- 不应该在提交消息中提及Claude或Anthropic。
- 应该将提交消息结构化如下：
<type>[optional scope]: <description>
[optional body]
[optional footer(s)]
- 提交应该包含以下结构元素来传达意图：
feat: 引入新的套利功能（对应语义版本控制中的MINOR）。
fix: 修复套利系统中的错误（对应语义版本控制中的PATCH）。
perf: 提高套利执行性能的代码变更。
refactor: 既不修复错误也不添加功能的代码变更。
test: 添加缺失的测试或纠正现有测试。
docs: 仅文档变更。
```

### QARB

当我输入"qarb"时，这意味着：

```
专门针对期现套利系统的检查：
1. 验证是否使用了统一模块（31个核心模块）
2. 检查是否满足性能要求（<30ms机会发现，<100ms执行）
3. 确认是否实现了原子快照技术
4. 验证对冲质量是否达到98%要求
5. 检查是否正确处理了异步调用和精度问题
```